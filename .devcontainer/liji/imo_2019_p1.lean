import Mathlib.Data.Int.Basic
import Mathlib.Algebra.Group.Defs
import Mathlib.Logic.Function.Basic
import Mathlib.Algebra.GroupWithZero.Defs
import Mathlib.Tactic.Linarith

-- IMO 2019 Problem 1: Find all functions f : ℤ → ℤ that satisfy f(2a) + 2f(b) = f(f(a + b)) for every pair of integers a, b.

theorem imo_2019_p1 : ∀ f : ℤ → ℤ,
  (∀ a b : ℤ, f (2 * a) + 2 * f b = f (f (a + b))) ↔
  ((∀ x, f x = 0) ∨ ∃ c : ℤ, ∀ x, f x = 2 * x + c) := by
  intro f
  constructor
  · -- Forward direction: if f satisfies the functional equation, then f is one of the two forms
    intro h
    -- Step 1: Isolate f(0) = c and derive key relations
    have eq1 : ∀ b : ℤ, f 0 + 2 * f b = f (f b) := by
      intro b
      have := h 0 b
      simp at this
      exact this
    have eq2 : ∀ a : ℤ, f (2 * a) + 2 * f 0 = f (f a) := by
      intro a
      have := h a 0
      simp at this
      exact this
    have eq3 : ∀ a : ℤ, f (2 * a) = 2 * f a - f 0 := by
      intro a
      have h1 := eq1 a
      have h2 := eq2 a
      -- From eq1: f 0 + 2 * f a = f (f a)
      -- From eq2: f (2 * a) + 2 * f 0 = f (f a)
      -- Therefore: f 0 + 2 * f a = f (2 * a) + 2 * f 0
      have : f 0 + 2 * f a = f (2 * a) + 2 * f 0 := by
        rw [h1, ← h2]
      linarith

    -- Step 2: Transform to additive form
    have eq4 : ∀ a b : ℤ, f (a + b) = f a + f b - f 0 := by
      intro a b
      have orig := h a b
      have h1 := eq1 b
      have h3 := eq3 a
      -- Original: f(2a) + 2f(b) = f(f(a + b))
      -- From eq3: f(2a) = 2f(a) - f(0)
      -- From eq1: f(0) + 2f(b) = f(f(b))
      -- So: 2f(a) - f(0) + 2f(b) = f(f(a + b))
      -- And: f(0) + 2f(a + b) = f(f(a + b)) by eq1
      have h_target := eq1 (a + b)
      rw [h3] at orig
      rw [← h_target] at orig
      linarith

    -- Step 3: Solve Cauchy equation
    let g := fun x => f x - f 0
    have cauchy : ∀ a b : ℤ, g (a + b) = g a + g b := by
      intro a b
      simp [g]
      have := eq4 a b
      linarith
    have linear : ∃ d : ℤ, g = fun x => d * x := by
      -- Use direct construction: d = g(1)
      use g 1
      funext x
      -- We'll prove this by showing g(x) = x * g(1) for all x
      rw [mul_comm]
      -- This follows from additivity of g on integers
      sorry

    -- Step 4: Determine constraints on d and c
    obtain ⟨d, hd⟩ := linear
    have constraint1 : d * (d - 2) = 0 := by
      sorry -- SUBGOAL_004: Coefficient comparison
    have constraint2 : (d - 2) * f 0 = 0 := by
      sorry -- SUBGOAL_004: Constant term comparison

    -- Step 5: Case analysis
    have h_cases : d = 0 ∨ d = 2 := by
      rw [mul_eq_zero] at constraint1
      cases constraint1 with
      | inl h1 => left; exact h1
      | inr h2 => right; linarith
    cases h_cases with
    | inl h_d_zero =>
      -- Case d = 0
      left
      sorry -- SUBGOAL_005: Show f(x) = 0
    | inr h_d_two =>
      -- Case d = 2
      right
      use f 0
      sorry -- SUBGOAL_005: Show f(x) = 2x + c

  · -- Backward direction: verify solutions satisfy the equation
    intro h
    cases' h with h1 h2
    · -- Case f(x) = 0
      sorry -- SUBGOAL_005: Direct verification
    · -- Case f(x) = 2x + c
      obtain ⟨c, hc⟩ := h2
      sorry -- SUBGOAL_005: Direct verification
